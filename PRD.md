  # Pixiv 标签下载器 (Rust 版本) - 产品需求文档

## 1. 项目概述

本项目旨在开发一个 **Rust 应用程序**，用于根据用户指定的 Pixiv 用户ID (UID) 下载其作品（包括图片、插画、漫画、小说）。程序通过 **YAML 配置文件** 管理所有配置参数（包括认证信息），并提供美观的交互式界面或命令行参数供用户指定需要下载的作品标签及其他选项。

下载过程采用 **Rust 的异步机制 (如 `tokio`)** 实现高效并发处理，支持在获取下一个作品元数据的同时下载当前作品，显著提高整体吞吐量。程序加入可配置的随机延迟以避免触发 Pixiv 的访问频率限制。

下载的内容按照用户可高度自定义的目录结构和文件命名规则进行组织和保存，**单图作品和多图作品支持独立的路径配置**。图片/漫画类作品将生成包含元数据的 TXT 文件；小说则直接保存为包含元数据和内容的 TXT 文件。

程序支持多种使用方式：**默认交互式命令行操作**（无参数时自动进入）、通过命令行参数直接执行任务。项目强调模块化、高内聚、低耦合的设计原则，利用 Rust 的强类型系统和所有权机制确保代码的健壮性、安全性和易于维护性与扩展性。

**所有用户界面、提示信息和代码注释均使用简体中文**，提供完善的本地化体验。

程序的名称及可执行程序名称应为 PixivTagDownloader，作者名称为 Mannix Sun，邮箱为 <EMAIL>。

## 2. 术语定义

- **UID:** Pixiv 用户的唯一标识符。
- **PID:** Pixiv 作品（图片、插画、漫画、小说）的唯一标识符。
- **标签 (Tag):** 用户为作品添加的标签，用于分类和搜索。
- **元数据 (Metadata):** 描述作品信息的数据，如标题、标签、描述、系列信息、作者、上传日期等。
- **模块化 (Modularity):** 将程序分解为独立的、可重用的功能单元（Rust 中的 `mod`）。
- **高内聚 (High Cohesion):** 模块内部的元素（函数、结构体、枚举等）联系紧密，共同完成一个明确的任务。
- **低耦合 (Low Coupling):** 模块之间的依赖关系尽可能少，一个模块的修改对其他模块的影响最小。
- **Crate:** Rust 的编译单元和包。
- **异步 (Asynchronous):** 一种并发编程模型，允许程序在等待 I/O 操作（如网络请求）完成时执行其他任务，常用 `async/await` 语法。
- **YAML (YAML Ain't Markup Language):** 一种人类可读的数据序列化标准，用于配置文件。
- **单图作品:** 只包含一张图片的插画或漫画作品。
- **多图作品:** 包含多张图片的插画集或漫画作品。
- **并发处理:** 同时执行多个任务以提高程序性能和响应速度。

## 3. 功能需求 (Functional Requirements)

### 3.1 配置管理与用户认证 (FR-CONFIG)

- **FR-CONFIG-01:** 程序启动时，必须能够读取指定路径（默认为当前目录下的 `config.yaml`，或通过命令行参数 `--config` 指定）的 YAML 配置文件。
- **FR-CONFIG-02:** 如果配置文件不存在，程序应自动创建一个包含所有可配置项的示例配置文件，并提供详细的中文注释说明每个配置项的作用和可选值。
- **FR-CONFIG-03:** 配置文件应包含但不限于以下配置项：
  - 认证信息（Pixiv 登录凭据）
  - 下载设置（并发数、延迟范围、重试次数等）
  - 文件组织设置（输出目录、单图/多图作品的路径模板和命名规则）
  - 网络设置（User-Agent、Referer、代理等）
  - 日志设置（级别、输出位置等）
- **FR-CONFIG-04:** 程序需使用配置文件中的认证信息模拟登录 Pixiv 账户，以获取访问用户作品的权限。应能验证认证信息的有效性（例如通过访问需要登录的 API 端点）。
- **FR-CONFIG-05:** 如果配置文件格式不正确或认证信息失效，程序应清晰地提示错误信息（使用简体中文），并指导用户如何修正配置文件。

  ### 3.2 用户交互与作品筛选 (FR-UI)

- **FR-UI-01:** 程序执行时如果无命令行参数，应默认进入交互模式，提供美观友好的中文交互界面。
- **FR-UI-02:** （交互模式下）程序成功认证后，应使用清晰的中文提示用户输入目标用户的 Pixiv UID。
- **FR-UI-03:** 程序需验证用户输入的 UID 格式是否有效（纯数字）。若无效，应使用中文提示错误并要求重新输入。

- **FR-UI-04:** （交互模式下）获取指定 UID 用户的作品元数据后，程序需能提取这些作品包含的所有唯一标签。
- **FR-UI-05:** （交互模式下）程序应首先询问用户是直接输入需要下载的标签（支持多个标签，逗号分隔），还是从该 UID 用户的所有作品中提取标签列表供选择。
- **FR-UI-06:** （交互模式下，若用户选择从列表选择）程序应向用户展示所有提取到的标签列表，并支持分页或搜索功能（如果标签过多）。
- **FR-UI-07:** （交互模式下）程序应允许用户交互式选择一个或多个标签作为过滤条件。

  - FR-UI-07:

     （交互模式下或通过参数指定）程序应支持用户选择Tag过滤逻辑：

    - **“且” (AND):** 作品需同时包含所有选定Tag。
    - **“或” (OR):** 作品只需包含任意一个选定Tag。
    - **(可选增强) “非” (NOT):** 排除包含指定Tag的作品。

  - **FR-UI-08:** 用户完成Tag选择（或选择下载全部，不进行Tag过滤，或通过命令行参数指定）后，程序应确认下载任务并开始执行。

  - **FR-UI-09:** 在下载过程中，应提供清晰的进度指示（例如，已下载作品数/总作品数，当前文件下载进度）。

  - **FR-UI-10:** 程序应允许用户选择需要下载的作品类型， 可多选或单选。

### 3.3 数据获取与处理 (FR-DATA)

- **FR-DATA-01:** 根据用户输入的 UID，程序需要能获取该用户的基本信息，特别是 **用户名 (Username)**，用于后续文件路径和元数据生成。获取到的用户名需进行文件名安全处理。
- **FR-DATA-02:** 程序需要能获取指定 UID 用户发布的所有作品列表，包含作品类型（插画、漫画、小说）、PID、标题、标签、系列信息（如果存在）、图片 URL（对于图片/漫画，需处理原始高质量图片链接）、小说内容（对于小说）等元数据。此过程可能涉及多次 API 请求和分页处理。
- **FR-DATA-03:** **内容类型检测：** 程序必须使用 Pixiv API 返回的 `type` 字段来准确判断作品类型（插画/漫画/小说），而不应基于图片数量进行判断。漫画作品可能只有一张图片，插画作品也可能包含多张图片。
- **FR-DATA-04:** 程序需根据用户选择的标签和过滤逻辑（AND/OR/NOT）过滤作品列表，确定最终需要下载的作品。
- **FR-DATA-05:** 对于包含多张图片的作品（无论是插画还是漫画），程序需要能获取所有页面的图片 URL，并确保下载时顺序正确。
- **FR-DATA-06:** 获取到的所有文本数据（标题、描述、标签等）应确保正确的 UTF-8 编码处理。

### 3.4 下载功能 (FR-DL)

- **FR-DL-01:** 程序应使用 **直接 HTTP 下载方式**，基于 Rust 的异步运行时（如 `tokio` 配合 `reqwest` crate）实现并发下载符合条件的作品，以提高下载效率。并发任务数量应可配置（配置文件或命令行参数），并提供合理的默认值。
- **FR-DL-02:** **并发性能优化：** 程序应实现流水线式处理，在下载当前作品的同时获取下一个作品的元数据，显著提高整体吞吐量和用户体验。
- **FR-DL-03:** 在每次对 Pixiv 服务器的网络请求（获取作品信息、下载文件等）之间，程序应加入一个随机时间延迟（例如，1-3 秒之间，范围可配置），以模拟人类行为，降低被 Pixiv 服务器阻止的风险。
- **FR-DL-04:** 下载过程中应优雅地处理可能发生的网络错误（如超时、连接失败、HTTP 错误码），并进行可配置的重试（次数和间隔）。无法成功下载的作品应记录错误详情并跳过，不中断整体任务。
- **FR-DL-05:** 程序应能处理 Pixiv 可能的反爬虫机制，如需要设置合适的 User-Agent、Referer 等 HTTP 头。这些 HTTP 头应允许用户通过配置文件进行自定义。
- **FR-DL-06:** 支持断点续传功能，避免网络中断导致的重复下载。

### 3.5 文件存储与组织 (FR-STORE)

- **FR-STORE-01:** 所有下载的内容默认保存在程序运行目录下的 `Output` 文件夹内，但必须允许用户在配置文件或通过命令行参数自定义输出根目录路径。

- **FR-STORE-02:** **独立路径配置：** 程序应为单图作品和多图作品提供独立的文件路径结构和命名规则配置，允许用户在 YAML 配置文件中分别指定：
  - `single_image_path_template`: 单图作品的目录结构模板
  - `single_image_filename_template`: 单图作品的文件命名模板
  - `multi_image_path_template`: 多图作品的目录结构模板
  - `multi_image_filename_template`: 多图作品的文件命名模板
  - `novel_path_template`: 小说作品的目录结构模板
  - `novel_filename_template`: 小说作品的文件命名模板

- **FR-STORE-03:** 应允许用户在配置文件中通过变量组合高度自定义目录结构和文件命名模板。支持的变量应包括但不限于：
  - `{uid}`: Pixiv 用户 ID
  - `{username}`: Pixiv 用户名（经过文件名安全处理）
  - `{pid}`: 作品 ID
  - `{title}`: 作品标题（经过文件名安全处理）
  - `{type}`: 作品类型（例如：`Illust`, `Manga`, `Novel`）
  - `{page_index}`: 图片/漫画页码索引号（例如 `p0`, `p00`, `p000`，格式可配置）
  - `{page_count}`: 作品总页数
  - `{series_title}`: 系列名称（如果存在，经过文件名安全处理）
  - `{series_id}`: 系列 ID（如果存在）
  - `{upload_date}`: 作品上传日期，可指定格式（例如 `{upload_date:YYYYMMDD}` 或 `{upload_date:%Y-%m-%d}`）
  - `{tags}`: 作品标签（以指定分隔符连接，例如 `{tags:_}` 或 `{tags: #}`，经过文件名安全处理）
  - `{r18}`: 是否为 R18 作品（例如输出 `R18` 或空）
  - `{like_count}`: 点赞数
  - `{bookmark_count}`: 收藏数
  - `{ext}`: 文件原始扩展名（例如 `.jpg`, `.png`）

- **FR-STORE-04:** 若用户未自定义路径模板，则提供合理的默认目录结构：
  - 单图作品：`Output/{uid}_{username}/单图作品/{series_title_or_未分类}/`
  - 多图作品：`Output/{uid}_{username}/多图作品/{series_title_or_未分类}/{pid}_{title}/`
  - 小说作品：`Output/{uid}_{username}/小说/{series_title_or_未分类}/`

- **FR-STORE-05 (图片/插画/漫画):**
  - **文件保存：** 根据 FR-STORE-02 和 FR-STORE-03 的模板规则保存图片文件。
  - **文件命名：** 图片文件命名格式通过模板定义，默认为：
    - 单图作品：`{upload_date:%Y%m%d}_{pid}_{title}.{ext}`
    - 多图作品：`{upload_date:%Y%m%d}_{pid}_p{page_index}_{title}.{ext}`
  - **元数据文件：**
    - 对于**单图作品**：在图片文件旁边创建一个同基本名但扩展名为 `.txt` 的元数据文件（例如 `artwork.jpg` -> `artwork.txt`）。
    - 对于**多图作品**：在该作品 PID 对应的目录下，创建一个统一的元数据文件，例如 `_metadata.txt` 或 `{pid}_metadata.txt`（文件名可配置）。
  - **元数据文件内容（TXT 格式，Key: Value 形式）：**
    - `标题: [作品标题]`
    - `作者UID: [作者UID]`
    - `作者用户名: [作者用户名]`
    - `作品PID: [作品PID]`
    - `作品类型: [作品类型，例如 插画, 漫画]`
    - `标签: [tag1, tag2, tag3]`（以逗号和空格分隔，或可配置）
    - `描述: \n[作品描述内容，保留换行]`
    - `系列标题: [系列标题]`（如果存在）
    - `系列ID: [系列ID]`（如果存在）
    - `上传日期: [上传日期和时间，ISO 8601 格式或可配置格式]`
    - `页数: [总页数]`
    - `R18: [是/否]`
    - `点赞数: [点赞数]`
    - `收藏数: [收藏数]`
    - `原始URL: \n[原始图片URL列表，每行一个]`（可选）
    - `下载时间: [本次下载完成时间，ISO 8601 格式]`

- **FR-STORE-06 (小说):**
  - **文件保存：** 小说文件保存位置遵循 FR-STORE-02 的模板规则。
  - **文件命名：** 小说文件命名格式通过模板定义，默认为 `{upload_date:%Y%m%d}_{pid}_{title}.txt`。文件名中的非法字符需要被自动替换或移除。
  - **文件内容（TXT 格式）：**
    - `标题: [小说标题]`
    - `作者UID: [作者UID]`
    - `作者用户名: [作者用户名]`
    - `小说PID: [小说PID]`
    - `作品类型: 小说`
    - `上传日期: [上传日期和时间，ISO 8601 格式或可配置格式]`
    - `标签: [tag1, tag2, tag3]`（以逗号和空格分隔，或可配置）
    - `系列标题: [系列标题]`（如果存在）
    - `系列ID: [系列ID]`（如果存在）
    - `R18: [是/否]`
    - `点赞数: [点赞数]`
    - `收藏数: [收藏数]`
    - `字数: [字数]`
    - `描述: \n[小说描述内容，保留换行]`
    - `下载时间: [本次下载完成时间，ISO 8601 格式]`
    - `--- 正文内容 ---`（分隔符可配置）
    - `\n[小说正文内容，保留原始换行和格式]`

- **FR-STORE-07:** 程序在下载文件前应检查目标路径是否存在同名文件。提供可配置的冲突处理策略：
  - **跳过：** 默认行为。
  - **覆盖：** 覆盖已存在文件。
  - **重命名：** 为新文件添加序号（例如 `file (1).ext`, `file (2).ext`）。

- **FR-STORE-08:** 文件名和路径中应严格处理操作系统不允许的非法字符（根据 Windows、Linux、macOS 的规则进行替换或移除，替换字符可配置），并处理路径过长的问题（例如通过截断文件名并提示，或使用部分哈希值缩短路径组件，需确保唯一性）。

- **FR-STORE-09:** 元数据文件和小说中的文本内容应统一使用 UTF-8 编码保存。

### 3.6 程序执行方式与接口 (FR-EXEC)

- **FR-EXEC-01:** 程序应支持两种不同的执行方式：
  - **交互式操作（默认）：** 用户直接执行程序时，如果无命令行参数，程序应默认进入交互模式，通过美观友好的中文命令行交互界面进行操作，程序引导用户完成各项设置和选择。使用如 `dialoguer` 或类似 crate 实现。
  - **命令行参数：** 用户可通过命令行参数一次性指定所有必要的操作参数，程序直接执行下载任务而无需交互。使用如 `clap` crate 进行参数解析。

- **FR-EXEC-02:** 命令行参数应全面，并能覆盖配置文件中的大部分设置，至少包含：
  - `-u, --uid <UID>`: 指定 Pixiv 用户 ID（必需）。
  - `-t, --tags <TAGS>`: 指定要下载的标签，多个标签用逗号分隔。
  - `-l, --logic <LOGIC>`: 指定标签过滤逻辑，可选值为 `and`、`or` 或 `not`（默认为 `or`）。
  - `--output-dir <PATH>`: 指定输出根目录。
  - `--config <PATH>`: 指定自定义配置文件路径。
  - `--threads <NUM>` 或 `--concurrency <NUM>`: 指定并发下载任务数。
  - `--delay <MIN_MAX_SECONDS>`: 指定随机延迟范围（例如 `1-3`）。
  - `--skip-existing` / `--overwrite-existing` / `--rename-existing`: 文件冲突处理策略。
  - `--log-level <LEVEL>`: 设置日志级别（例如 `trace`, `debug`, `info`, `warn`, `error`）。
  - `--all`: 下载用户所有作品，不进行标签筛选。
  - `--type <type>`: 指定要下载的作品类型（例如 `插画`, `漫画`, `小说`, `插画,漫画`, `全部`），大小写不敏感，可单选或多选，多个类型用逗号分隔，默认为 `全部`。

## 4. 非功能需求 (Non-Functional Requirements)

- **NFR-DESIGN-01 (模块化):** 代码结构应清晰地划分为不同的模块（例如：`config`, `api`, `downloader`, `storage`, `cli`, `core_logic` 等），利用 Rust 的模块系统。
- **NFR-DESIGN-02 (高内聚):** 每个模块应专注于完成单一的功能（例如：`config` 模块只负责配置文件管理）。
- **NFR-DESIGN-03 (低耦合):** 模块之间的依赖应最小化，通过定义良好的接口（trait、struct、enum）或标准数据格式进行交互。
- **NFR-PERF-01 (性能):** 利用 Rust 的异步运行时（如 `tokio`）、以及零成本抽象特性，实现高效并发下载和数据处理。实现流水线式处理，在下载当前作品的同时获取下一个作品的元数据。优化内存使用。
- **NFR-ROBUST-01 (健壮性):** 使用 Rust 的 `Result<T, E>` 和 `Option<T>` 类型进行全面的错误处理，避免 `panic` 在可恢复的错误场景。对 Pixiv API 可能的变更提供一定的兼容性或清晰的错误提示，并记录详细的错误日志。
- **NFR-SEC-01 (安全性):** 避免硬编码敏感信息。认证信息不应被随意记录到日志中（除非是 Debug 级别且有明确提示）。
- **NFR-USABILITY-01 (易用性):** 交互式命令行界面应清晰、直观，使用简体中文，易于用户理解和操作。命令行参数设计应符合常见 CLI 工具的惯例。错误信息应友好且具有指导性。
- **NFR-MAINTAIN-01 (可维护性):** 代码应遵循 Rust 社区的编码规范（使用 `rustfmt` 格式化，通过 `clippy` 进行静态检查）。添加完善的中文文档注释，以便通过 `cargo doc` 生成 API 文档。代码逻辑清晰，易于理解和修改。
- **NFR-COMPLIANCE-01 (合规性):** 加入随机延迟，避免对 Pixiv 服务器造成过大负担。在程序文档和首次运行时，明确提示用户应遵守 Pixiv 的使用条款，并自行承担下载内容的版权责任。程序仅供个人学习、研究和合法备份用途。
- **NFR-CONFIG-01 (可配置性):** 程序应支持通过 YAML 格式的配置文件（`config.yaml`，路径可指定）自定义各项设置。如果配置文件不存在，应自动创建包含详细中文注释的示例配置文件。命令行参数优先级高于配置文件，配置文件高于程序内置默认值。
- **NFR-PACKAGE-01 (可分发性):** 程序应使用 Cargo 进行构建和管理，方便编译为针对不同平台（Windows, Linux, macOS）的本地可执行文件。如果开源，应易于通过 `cargo install` 或从 GitHub Releases 获取。
- **NFR-LOGGING-01 (日志):** 实现分级别的日志系统（例如使用 `log` 和 `env_logger` 或 `tracing` crates）。日志级别可通过命令行参数或配置文件设置。日志可输出到控制台和/或指定的文件。日志内容应包含时间戳、级别、模块和消息，并使用简体中文。
- **NFR-I18N-01 (本地化):** 程序的所有用户界面文本、提示信息、错误消息和日志输出均使用简体中文，提供完整的本地化体验。代码注释也应使用简体中文，便于中文开发者理解和维护。

## 5. 输入与输出

- **输入：**
  - `config.yaml` 配置文件（包含认证信息和所有配置参数）。
  - 用户通过命令行或交互界面输入的 Pixiv UID。
  - 用户通过命令行或交互界面选择的标签（或选择不筛选）。
  - 用户通过命令行或交互界面选择的标签过滤逻辑（AND/OR/NOT）。
  - 用户通过命令行或交互界面选择的作品类型（插画/漫画/小说）。
  - 其他命令行参数。

- **输出：**
  - 下载的图片/插画/漫画文件（原始格式，如 JPG, PNG, GIF）。
  - 为每个图片/漫画作品生成的元数据 `.txt` 文件（使用中文字段名）。
  - 下载的小说 `.txt` 文件（包含元数据和内容，使用中文字段名）。
  - 按照用户指定规则组织的目录结构（支持单图/多图作品独立配置）。
  - 程序运行过程中的状态信息、进度提示、警告和错误消息（使用简体中文，打印到控制台和/或日志文件）。

## 6. 约束与假设 (Constraints & Assumptions)

- **CON-ENV-01:** 程序需要运行在安装了 Rust 工具链（包括 `cargo` 和 `rustc`，建议最新稳定版）的环境中才能编译。最终用户只需可执行文件。

- **CON-LIB-01:** 程序将依赖一系列高质量的 Rust crates，例如：
  - **HTTP 客户端：** `reqwest`（及其对 `tokio` 的集成）。
  - **异步运行时：** `tokio`（首选）。
  - **序列化/反序列化：** `serde`, `serde_json`, `serde_yaml`。
  - **HTML/XML 解析（如果需要）：** `scraper` 或 `quick-xml`。
  - **命令行参数解析：** `clap`。
  - **交互式 CLI：** `dialoguer`。
  - **进度条：** `indicatif`。
  - **错误处理：** `thiserror`, `anyhow`。
  - **日志：** `log`, `env_logger` 或 `tracing`。
  - **文件系统操作：** 标准库 `std::fs`, `std::path`。

- **CON-NET-01:** 需要稳定且未受限制的互联网连接才能访问 Pixiv。

- **CON-PIXIV-01:** 程序高度依赖于当前 Pixiv 网站的结构和其（很可能是非官方的）API 接口。Pixiv 的任何重大更新（如 API 变更、反爬虫策略调整）都可能导致程序功能失效或部分失效，需要开发者及时维护更新。**用户应被明确告知此风险。**

- **CON-CONFIG-01:** 假设用户在配置文件中提供的认证信息是有效的、未过期的，并且具有访问目标用户作品（包括可能存在的 R18 内容，若用户期望下载）的权限。程序不负责获取认证信息。

- **CON-RATE-01:** 随机延迟和并发数量的设置旨在降低风险，但不能 100% 保证不触发 Pixiv 的访问频率限制或 IP 封锁。用户应合理配置这些参数。

- **CON-COMPAT-01:** 程序应力求在主流桌面操作系统（Windows 10/11, macOS, Linux）上表现一致。文件路径和文件名处理需特别注意跨平台兼容性。

- **CON-LEGAL-01 (法律与合规):** 用户应自行承担使用本程序下载、存储和使用任何受版权保护内容的全部法律责任。程序开发者不对此负责。程序设计初衷仅为个人学习、研究和对已授权内容的合法备份，严禁用于非法传播、商业牟利或任何侵犯版权的行为。

- **CON-RELEASE-01:** 程序的 target 应包含 `x86_64-unknown-linux-musl`, `x86_64-pc-windows-gnu`。

## 7. 用户故事 (User Stories)

### 7.1 首次使用用户
**作为** 一个首次使用的用户，**我希望** 程序能够自动创建配置文件模板，**以便** 我能够快速了解和配置程序。

**验收标准：**
- 程序首次运行时检测到配置文件不存在
- 自动创建包含详细中文注释的示例配置文件
- 提示用户配置文件的位置和需要修改的内容

### 7.2 交互式用户
**作为** 一个偏好图形界面的用户，**我希望** 程序提供美观的交互式界面，**以便** 我能够轻松地进行操作而无需记忆复杂的命令行参数。

**验收标准：**
- 无参数启动时自动进入交互模式
- 提供清晰的中文菜单和提示
- 支持标签选择、过滤逻辑配置等交互操作
- 显示实时进度和状态信息

### 7.3 高级用户
**作为** 一个熟悉命令行的高级用户，**我希望** 能够通过命令行参数快速执行下载任务，**以便** 我能够将程序集成到自动化脚本中。

**验收标准：**
- 支持完整的命令行参数配置
- 命令行参数能够覆盖配置文件设置
- 提供批处理和自动化支持

### 7.4 内容收集者
**作为** 一个需要下载大量作品的用户，**我希望** 程序能够高效地并发下载，**以便** 我能够在合理的时间内完成大批量下载任务。

**验收标准：**
- 支持并发下载多个作品
- 在下载当前作品的同时获取下一个作品的元数据
- 提供可配置的并发数量和延迟设置
- 显示详细的进度信息和剩余时间估算

## 8. 技术规范 (Technical Specifications)

### 8.1 配置文件示例
程序应自动生成的 `config.yaml` 示例文件应包含以下结构：

```yaml
# Pixiv 标签下载器配置文件
# 请根据需要修改以下配置项

# 认证配置
auth:
  # Pixiv 登录凭据（请填入有效的认证信息）
  session_id: "your_session_id_here"
  csrf_token: "your_csrf_token_here"

# 下载配置
download:
  # 并发下载数量（建议 1-5）
  concurrency: 3
  # 请求延迟范围（秒）
  delay_range: [1, 3]
  # 重试次数
  retry_count: 3
  # 超时时间（秒）
  timeout: 30

# 文件组织配置
storage:
  # 输出根目录
  output_dir: "./Output"

  # 单图作品配置
  single_image:
    path_template: "{uid}_{username}/单图作品/{series_title_or_未分类}"
    filename_template: "{upload_date:%Y%m%d}_{pid}_{title}.{ext}"

  # 多图作品配置
  multi_image:
    path_template: "{uid}_{username}/多图作品/{series_title_or_未分类}/{pid}_{title}"
    filename_template: "{upload_date:%Y%m%d}_{pid}_p{page_index}_{title}.{ext}"

  # 小说配置
  novel:
    path_template: "{uid}_{username}/小说/{series_title_or_未分类}"
    filename_template: "{upload_date:%Y%m%d}_{pid}_{title}.txt"

  # 文件冲突处理：skip（跳过）、overwrite（覆盖）、rename（重命名）
  conflict_resolution: "skip"

# 网络配置
network:
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  referer: "https://www.pixiv.net/"

# 日志配置
logging:
  level: "info"  # trace, debug, info, warn, error
  file: "./pixiv_downloader.log"
  console: true
```

### 8.2 并发处理架构
程序应实现以下并发处理模式：
1. **元数据获取线程池**：负责获取作品信息和 URL
2. **下载线程池**：负责实际的文件下载
3. **流水线处理**：元数据获取和文件下载并行进行
4. **队列管理**：使用异步队列协调不同阶段的任务

### 8.3 错误处理策略
- 网络错误：自动重试，记录详细错误信息
- 认证失败：提示用户检查配置文件
- 文件系统错误：提供清晰的中文错误信息
- API 变更：提供兼容性检查和错误提示

## 9. 实现指南 (Implementation Guidelines)

### 9.1 项目结构建议
```
src/
├── main.rs                 # 程序入口点
├── config/                 # 配置管理模块
│   ├── mod.rs
│   ├── yaml_config.rs      # YAML 配置文件处理
│   └── default_config.rs   # 默认配置生成
├── api/                    # Pixiv API 交互模块
│   ├── mod.rs
│   ├── client.rs           # HTTP 客户端
│   ├── auth.rs             # 认证处理
│   └── endpoints.rs        # API 端点定义
├── downloader/             # 下载模块
│   ├── mod.rs
│   ├── concurrent.rs       # 并发下载管理
│   ├── pipeline.rs         # 流水线处理
│   └── progress.rs         # 进度跟踪
├── storage/                # 文件存储模块
│   ├── mod.rs
│   ├── organizer.rs        # 文件组织
│   ├── metadata.rs         # 元数据处理
│   └── path_template.rs    # 路径模板解析
├── cli/                    # 命令行界面模块
│   ├── mod.rs
│   ├── interactive.rs      # 交互式界面
│   ├── args.rs             # 命令行参数解析
│   └── ui_components.rs    # UI 组件
├── core/                   # 核心逻辑模块
│   ├── mod.rs
│   ├── artwork.rs          # 作品数据结构
│   ├── filter.rs           # 标签过滤逻辑
│   └── types.rs            # 类型定义
└── utils/                  # 工具模块
    ├── mod.rs
    ├── file_utils.rs       # 文件操作工具
    ├── string_utils.rs     # 字符串处理工具
    └── error.rs            # 错误类型定义
```

### 9.2 关键实现要点

#### 9.2.1 配置文件自动生成
```rust
// 示例代码结构
pub fn ensure_config_exists(config_path: &Path) -> Result<(), ConfigError> {
    if !config_path.exists() {
        create_default_config(config_path)?;
        println!("已创建示例配置文件：{}", config_path.display());
        println!("请编辑配置文件后重新运行程序。");
    }
    Ok(())
}
```

#### 9.2.2 内容类型检测
```rust
// 基于 API 返回的 type 字段进行判断
pub fn determine_artwork_type(api_response: &PixivArtwork) -> ArtworkType {
    match api_response.type_field.as_str() {
        "illust" => ArtworkType::Illustration,
        "manga" => ArtworkType::Manga,
        "novel" => ArtworkType::Novel,
        _ => ArtworkType::Unknown,
    }
}
```

#### 9.2.3 并发流水线处理
```rust
// 并发处理架构示例
pub async fn process_artworks_pipeline(
    artwork_ids: Vec<u64>,
    config: &Config,
) -> Result<(), DownloadError> {
    let (metadata_tx, metadata_rx) = tokio::sync::mpsc::channel(100);
    let (download_tx, download_rx) = tokio::sync::mpsc::channel(50);

    // 元数据获取任务
    let metadata_task = tokio::spawn(fetch_metadata_worker(artwork_ids, metadata_tx));

    // 下载任务
    let download_task = tokio::spawn(download_worker(metadata_rx, download_tx));

    // 进度跟踪任务
    let progress_task = tokio::spawn(progress_tracker(download_rx));

    tokio::try_join!(metadata_task, download_task, progress_task)?;
    Ok(())
}
```

### 9.4 部署和分发
- 使用 GitHub Actions 进行 CI/CD
- 支持多平台交叉编译
- 提供预编译的二进制文件
- 包含详细的中文使用文档

## 10. 总结

本产品需求文档详细描述了 Pixiv 标签下载器的完整功能需求和技术规范。主要改进包括：

1. **简化架构**：移除 aria2 依赖，使用直接 HTTP 下载
2. **准确类型检测**：基于 API 返回的 type 字段而非图片数量
3. **统一配置管理**：使用 YAML 配置文件替代 cookie.txt
4. **智能配置生成**：自动创建带注释的示例配置文件
5. **灵活文件组织**：单图/多图作品独立路径配置
6. **完整中文化**：所有界面和注释使用简体中文
7. **优秀用户体验**：默认交互模式，美观的界面设计
8. **性能优化**：并发流水线处理，提高下载效率

该文档为开发团队提供了清晰的实现指导，确保最终产品能够满足用户需求并提供优秀的使用体验。
