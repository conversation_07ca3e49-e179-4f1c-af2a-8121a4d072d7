# Pixiv 标签下载器配置文件
# 请根据需要修改以下配置项

# 认证配置
auth:
  # Pixiv 登录凭据（请填入有效的认证信息）
  # 获取方法：登录 Pixiv 后，在浏览器开发者工具中查看 Cookie
  session_id: "your_session_id_here"
  csrf_token: "your_csrf_token_here"

# 下载配置
download:
  # 并发下载数量（建议 1-5，过高可能被限制）
  concurrency: 3
  # 请求延迟范围（秒），用于避免触发反爬虫机制
  delay_range: [1, 3]
  # 重试次数
  retry_count: 3
  # 超时时间（秒）
  timeout: 30

# 文件组织配置
storage:
  # 输出根目录
  output_dir: "./Output"

  # 单图作品配置
  single_image:
    # 路径模板，支持变量：{uid}, {username}, {series_title_or_未分类} 等
    path_template: "{uid}_{username}/单图作品/{series_title_or_未分类}"
    # 文件名模板，支持变量：{upload_date}, {pid}, {title}, {ext} 等
    filename_template: "{upload_date:%Y%m%d}_{pid}_{title}.{ext}"

  # 多图作品配置
  multi_image:
    path_template: "{uid}_{username}/多图作品/{series_title_or_未分类}/{pid}_{title}"
    filename_template: "{upload_date:%Y%m%d}_{pid}_p{page_index}_{title}.{ext}"

  # 小说配置
  novel:
    path_template: "{uid}_{username}/小说/{series_title_or_未分类}"
    filename_template: "{upload_date:%Y%m%d}_{pid}_{title}.txt"

  # 文件冲突处理策略
  # skip: 跳过已存在文件
  # overwrite: 覆盖已存在文件  
  # rename: 重命名新文件
  conflict_resolution: "skip"

# 网络配置
network:
  # User-Agent 字符串
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  # Referer 头
  referer: "https://www.pixiv.net/"
  # HTTP 代理（可选）
  # proxy: "http://127.0.0.1:8080"

# 日志配置
logging:
  # 日志级别：trace, debug, info, warn, error
  level: "info"
  # 日志文件路径（可选）
  file: "./pixiv_downloader.log"
  # 是否输出到控制台
  console: true

# 支持的路径模板变量说明：
# {uid} - Pixiv 用户 ID
# {username} - Pixiv 用户名（文件名安全处理）
# {pid} - 作品 ID
# {title} - 作品标题（文件名安全处理）
# {type} - 作品类型（Illust, Manga, Novel）
# {page_index} - 图片页码索引号（p0, p1, p2...）
# {page_count} - 作品总页数
# {series_title} - 系列名称（如果存在）
# {series_id} - 系列 ID（如果存在）
# {upload_date} - 上传日期，支持格式化（如 {upload_date:%Y-%m-%d}）
# {tags} - 作品标签（可指定分隔符，如 {tags:_}）
# {r18} - 是否为 R18 作品
# {like_count} - 点赞数
# {bookmark_count} - 收藏数
# {ext} - 文件扩展名
