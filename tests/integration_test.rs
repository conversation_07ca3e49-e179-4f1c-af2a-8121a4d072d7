/*!
 * 集成测试
 */

use pixiv_tag_downloader::config::Config;
use pixiv_tag_downloader::core::{TagFilter, TagFilterLogic, ArtworkTypeFilter};
use std::path::PathBuf;

#[tokio::test]
async fn test_config_loading() {
    // 测试默认配置创建
    let temp_dir = tempfile::tempdir().unwrap();
    let config_path = temp_dir.path().join("test_config.yaml");
    
    // 第一次加载应该创建默认配置文件
    let result = Config::load(&config_path).await;
    assert!(result.is_err()); // 应该返回错误，提示需要编辑配置
    assert!(config_path.exists()); // 配置文件应该被创建
}

#[test]
fn test_tag_filter() {
    let tags = vec!["原创".to_string(), "插画".to_string(), "风景".to_string()];
    
    // 测试 OR 逻辑
    let filter = TagFilter::new(vec!["原创".to_string()], TagFilterLogic::Or);
    assert!(filter.matches(&tags));
    
    let filter = TagFilter::new(vec!["不存在的标签".to_string()], TagFilterLogic::Or);
    assert!(!filter.matches(&tags));
    
    // 测试 AND 逻辑
    let filter = TagFilter::new(vec!["原创".to_string(), "插画".to_string()], TagFilterLogic::And);
    assert!(filter.matches(&tags));
    
    let filter = TagFilter::new(vec!["原创".to_string(), "不存在的标签".to_string()], TagFilterLogic::And);
    assert!(!filter.matches(&tags));
    
    // 测试 NOT 逻辑
    let filter = TagFilter::new(vec!["不存在的标签".to_string()], TagFilterLogic::Not);
    assert!(filter.matches(&tags));
    
    let filter = TagFilter::new(vec!["原创".to_string()], TagFilterLogic::Not);
    assert!(!filter.matches(&tags));
}

#[test]
fn test_artwork_type_filter() {
    use pixiv_tag_downloader::core::ArtworkType;
    
    let filter = ArtworkTypeFilter::All;
    assert!(filter.matches(&ArtworkType::Illust));
    assert!(filter.matches(&ArtworkType::Manga));
    assert!(filter.matches(&ArtworkType::Novel));
    
    let filter = ArtworkTypeFilter::Illust;
    assert!(filter.matches(&ArtworkType::Illust));
    assert!(!filter.matches(&ArtworkType::Manga));
    assert!(!filter.matches(&ArtworkType::Novel));
}

#[test]
fn test_path_template() {
    use pixiv_tag_downloader::storage::PathTemplateResolver;
    use pixiv_tag_downloader::core::{ArtworkInfo, UserInfo, ArtworkType, SeriesInfo};
    use pixiv_tag_downloader::config::{StorageConfig, PathTemplateConfig, ConflictResolution};
    use chrono::Utc;
    
    let resolver = PathTemplateResolver::new();
    
    let artwork = ArtworkInfo {
        pid: 123456,
        title: "测试作品".to_string(),
        artwork_type: ArtworkType::Illust,
        tags: vec!["原创".to_string(), "插画".to_string()],
        description: "测试描述".to_string(),
        author_uid: 789,
        author_username: "测试用户".to_string(),
        upload_date: Utc::now(),
        page_count: 1,
        is_r18: false,
        like_count: 100,
        bookmark_count: 50,
        series: Some(SeriesInfo {
            id: 1001,
            title: "测试系列".to_string(),
        }),
        image_urls: vec!["https://example.com/image.jpg".to_string()],
        novel_content: None,
    };
    
    let user_info = UserInfo {
        uid: 789,
        username: "test_user".to_string(),
        display_name: "测试用户".to_string(),
        avatar_url: None,
    };
    
    let storage_config = StorageConfig {
        output_dir: "./test_output".to_string(),
        single_image: PathTemplateConfig {
            path_template: "{uid}_{username}/单图作品/{series_title_or_未分类}".to_string(),
            filename_template: "{pid}_{title}.{ext}".to_string(),
        },
        multi_image: PathTemplateConfig {
            path_template: "{uid}_{username}/多图作品/{series_title_or_未分类}".to_string(),
            filename_template: "{pid}_p{page_index}_{title}.{ext}".to_string(),
        },
        novel: PathTemplateConfig {
            path_template: "{uid}_{username}/小说/{series_title_or_未分类}".to_string(),
            filename_template: "{pid}_{title}.txt".to_string(),
        },
        conflict_resolution: ConflictResolution::Skip,
    };
    
    let result = resolver.resolve_path(&artwork, &user_info, &storage_config, None);
    assert!(result.is_ok());
    
    let path = result.unwrap();
    let path_str = path.to_string_lossy();
    assert!(path_str.contains("789_test_user"));
    assert!(path_str.contains("123456"));
    assert!(path_str.contains("测试系列"));
}

#[test]
fn test_error_messages() {
    use pixiv_tag_downloader::utils::AppError;
    
    let error = AppError::config("测试配置错误");
    assert_eq!(error.user_message(), "配置文件有问题: 测试配置错误");
    assert!(error.suggestion().is_some());
    
    let error = AppError::auth("认证失败");
    assert_eq!(error.user_message(), "登录验证失败: 认证失败");
    assert!(error.suggestion().is_some());
}
