/*!
 * 核心类型定义
 */

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 作品类型
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum ArtworkType {
    /// 插画
    Illust,
    /// 漫画
    Manga,
    /// 小说
    Novel,
    /// 未知类型
    Unknown,
}

impl From<&str> for ArtworkType {
    fn from(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "illust" => Self::Illust,
            "manga" => Self::Manga,
            "novel" => Self::Novel,
            _ => Self::Unknown,
        }
    }
}

impl ArtworkType {
    /// 转换为中文描述
    pub fn to_chinese(&self) -> &'static str {
        match self {
            Self::Illust => "插画",
            Self::Manga => "漫画",
            Self::Novel => "小说",
            Self::Unknown => "未知",
        }
    }
}

/// 作品信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ArtworkInfo {
    /// 作品 ID
    pub pid: u64,
    /// 标题
    pub title: String,
    /// 作品类型
    pub artwork_type: ArtworkType,
    /// 标签列表
    pub tags: Vec<String>,
    /// 描述
    pub description: String,
    /// 作者 UID
    pub author_uid: u64,
    /// 作者用户名
    pub author_username: String,
    /// 上传日期
    pub upload_date: DateTime<Utc>,
    /// 页数
    pub page_count: u32,
    /// 是否为 R18
    pub is_r18: bool,
    /// 点赞数
    pub like_count: u32,
    /// 收藏数
    pub bookmark_count: u32,
    /// 系列信息（可选）
    pub series: Option<SeriesInfo>,
    /// 图片 URLs（对于图片/漫画）
    pub image_urls: Vec<String>,
    /// 小说内容（对于小说）
    pub novel_content: Option<String>,
}

/// 系列信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SeriesInfo {
    /// 系列 ID
    pub id: u64,
    /// 系列标题
    pub title: String,
}

/// 下载进度信息
#[derive(Debug, Clone)]
pub struct DownloadProgress {
    /// 总作品数
    pub total_artworks: usize,
    /// 已完成作品数
    pub completed_artworks: usize,
    /// 当前下载的作品
    pub current_artwork: Option<ArtworkInfo>,
    /// 当前文件下载进度（字节）
    pub current_file_progress: Option<FileProgress>,
}

/// 文件下载进度
#[derive(Debug, Clone)]
pub struct FileProgress {
    /// 文件名
    pub filename: String,
    /// 已下载字节数
    pub downloaded_bytes: u64,
    /// 总字节数（如果已知）
    pub total_bytes: Option<u64>,
}

/// 下载结果
#[derive(Debug, Clone)]
pub struct DownloadResult {
    /// 成功下载的作品数
    pub successful_downloads: usize,
    /// 失败的作品数
    pub failed_downloads: usize,
    /// 跳过的作品数
    pub skipped_downloads: usize,
    /// 错误列表
    pub errors: Vec<DownloadError>,
}

/// 下载错误
#[derive(Debug, Clone)]
pub struct DownloadError {
    /// 作品 ID
    pub pid: u64,
    /// 错误消息
    pub error: String,
    /// 错误类型
    pub error_type: DownloadErrorType,
}

/// 下载错误类型
#[derive(Debug, Clone)]
pub enum DownloadErrorType {
    /// 网络错误
    Network,
    /// 文件系统错误
    FileSystem,
    /// API 错误
    Api,
    /// 认证错误
    Authentication,
    /// 其他错误
    Other,
}
