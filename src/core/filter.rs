/*!
 * 标签过滤逻辑
 */

use crate::cli::TagLogic;

/// 标签过滤器
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct TagFilter {
    /// 标签列表
    pub tags: Vec<String>,
    /// 过滤逻辑
    pub logic: TagFilterLogic,
}

/// 标签过滤逻辑
#[derive(Debu<PERSON>, Clone)]
pub enum TagFilterLogic {
    /// 且（AND）
    And,
    /// 或（OR）
    Or,
    /// 非（NOT）
    Not,
}

impl From<TagLogic> for TagFilterLogic {
    fn from(logic: TagLogic) -> Self {
        match logic {
            TagLogic::And => Self::And,
            TagLogic::Or => Self::Or,
            TagLogic::Not => Self::Not,
        }
    }
}

impl TagFilter {
    /// 创建新的标签过滤器
    pub fn new(tags: Vec<String>, logic: TagFilterLogic) -> Self {
        Self { tags, logic }
    }
    
    /// 检查标签是否匹配
    pub fn matches(&self, artwork_tags: &[String]) -> bool {
        match self.logic {
            TagFilterLogic::And => {
                self.tags.iter().all(|tag| artwork_tags.contains(tag))
            }
            TagFilterLogic::Or => {
                self.tags.iter().any(|tag| artwork_tags.contains(tag))
            }
            TagFilterLogic::Not => {
                !self.tags.iter().any(|tag| artwork_tags.contains(tag))
            }
        }
    }
}
