/*!
 * 核心逻辑模块
 */

pub mod artwork;
pub mod filter;
pub mod types;
pub mod downloader;

use anyhow::Result;
use std::sync::Arc;

pub use artwork::*;
pub use filter::*;
pub use types::*;
pub use downloader::*;

use crate::config::Config;
use crate::api::PixivClient;

/// Pixiv 下载器主类
pub struct PixivDownloader {
    /// 配置
    config: Arc<Config>,
    /// API 客户端
    client: Arc<PixivClient>,
}

impl PixivDownloader {
    /// 创建新的下载器实例
    pub async fn new(config: Config) -> Result<Self> {
        let config = Arc::new(config);
        let client = Arc::new(PixivClient::new(config.clone()).await?);
        
        Ok(Self {
            config,
            client,
        })
    }
    
    /// 执行下载任务
    pub async fn download(&self, task: DownloadTask) -> Result<()> {
        use crate::downloader::ConcurrentDownloader;
        
        // 创建下载器
        let downloader = ConcurrentDownloader::new(
            self.config.clone(),
            self.client.clone(),
        );
        
        // 执行下载
        downloader.execute(task).await
    }
    
    /// 获取用户信息
    pub async fn get_user_info(&self, uid: u64) -> Result<UserInfo> {
        self.client.get_user_info(uid).await
    }
    
    /// 获取用户作品列表
    pub async fn get_user_artworks(&self, uid: u64) -> Result<Vec<ArtworkInfo>> {
        self.client.get_user_artworks(uid).await
    }
    
    /// 提取作品标签
    pub async fn extract_tags(&self, artworks: &[ArtworkInfo]) -> Vec<String> {
        let mut tags = std::collections::HashSet::new();
        
        for artwork in artworks {
            for tag in &artwork.tags {
                tags.insert(tag.clone());
            }
        }
        
        let mut tag_list: Vec<String> = tags.into_iter().collect();
        tag_list.sort();
        tag_list
    }
}

/// 下载任务
#[derive(Debug, Clone)]
pub struct DownloadTask {
    /// 用户 ID
    pub uid: u64,
    /// 标签过滤器（可选）
    pub tag_filter: Option<TagFilter>,
    /// 作品类型过滤器
    pub artwork_type_filter: ArtworkTypeFilter,
    /// 输出目录覆盖（可选）
    pub output_dir_override: Option<String>,
}

impl DownloadTask {
    /// 从命令行参数创建下载任务
    pub fn from_args(uid: u64, args: &crate::cli::Args) -> Result<Self> {
        let tag_filter = if args.all {
            None
        } else if let Some(tags) = args.parse_tags() {
            Some(TagFilter::new(tags, args.logic.clone().into()))
        } else {
            None
        };
        
        Ok(Self {
            uid,
            tag_filter,
            artwork_type_filter: args.artwork_type.clone().into(),
            output_dir_override: args.output_dir.as_ref().map(|p| p.to_string_lossy().to_string()),
        })
    }
}

/// 用户信息
#[derive(Debug, Clone)]
pub struct UserInfo {
    /// 用户 ID
    pub uid: u64,
    /// 用户名
    pub username: String,
    /// 显示名称
    pub display_name: String,
    /// 头像 URL
    pub avatar_url: Option<String>,
}

/// 作品类型过滤器
#[derive(Debug, Clone)]
pub enum ArtworkTypeFilter {
    /// 插画
    Illust,
    /// 漫画
    Manga,
    /// 小说
    Novel,
    /// 全部
    All,
}

impl From<crate::cli::ArtworkTypeFilter> for ArtworkTypeFilter {
    fn from(filter: crate::cli::ArtworkTypeFilter) -> Self {
        match filter {
            crate::cli::ArtworkTypeFilter::Illust => Self::Illust,
            crate::cli::ArtworkTypeFilter::Manga => Self::Manga,
            crate::cli::ArtworkTypeFilter::Novel => Self::Novel,
            crate::cli::ArtworkTypeFilter::All => Self::All,
        }
    }
}

impl ArtworkTypeFilter {
    /// 检查是否匹配作品类型
    pub fn matches(&self, artwork_type: &ArtworkType) -> bool {
        match self {
            Self::All => true,
            Self::Illust => matches!(artwork_type, ArtworkType::Illust),
            Self::Manga => matches!(artwork_type, ArtworkType::Manga),
            Self::Novel => matches!(artwork_type, ArtworkType::Novel),
        }
    }
}
