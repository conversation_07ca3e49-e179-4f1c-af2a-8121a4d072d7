/*!
 * Pixiv 标签下载器 (Rust 版本)
 * 
 * 作者: Mannix Sun <<EMAIL>>
 * 
 * 这是一个用于根据标签下载 Pixiv 用户作品的 Rust 应用程序。
 * 支持图片、插画、漫画和小说的下载，提供高效的并发处理和灵活的配置选项。
 */

use anyhow::Result;
use clap::Parser;
use std::path::PathBuf;
use tracing::{info, error};

// 模块声明
mod config;
mod api;
mod downloader;
mod storage;
mod cli;
mod core;
mod utils;

use config::Config;
use cli::{Args, interactive_mode};
use core::PixivDownloader;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统
    init_logging()?;
    
    info!("Pixiv 标签下载器启动");
    
    // 解析命令行参数
    let args = Args::parse();
    
    // 加载配置
    let config = match load_config(&args.config).await {
        Ok(config) => config,
        Err(e) => {
            eprintln!("配置加载失败: {}", e);
            return Err(e);
        }
    };
    
    // 验证配置
    if let Err(e) = config.validate() {
        error!("配置验证失败: {}", e);
        return Err(e);
    }
    
    // 创建下载器实例
    let downloader = PixivDownloader::new(config).await?;
    
    // 根据参数决定运行模式
    if args.is_interactive_mode() {
        // 交互式模式
        info!("进入交互式模式");
        interactive_mode(downloader).await?;
    } else {
        // 命令行模式
        info!("使用命令行参数模式");
        run_with_args(downloader, args).await?;
    }
    
    info!("程序执行完成");
    Ok(())
}

/// 初始化日志系统
fn init_logging() -> Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
    
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();
    
    Ok(())
}

/// 加载配置文件
async fn load_config(config_path: &Option<PathBuf>) -> Result<Config> {
    let path = config_path
        .clone()
        .unwrap_or_else(|| PathBuf::from("config.yaml"));
    
    Config::load(&path).await
}

/// 使用命令行参数运行
async fn run_with_args(downloader: PixivDownloader, args: Args) -> Result<()> {
    // 验证必需参数
    let uid = args.uid.ok_or_else(|| {
        anyhow::anyhow!("命令行模式下必须指定用户 UID (--uid)")
    })?;
    
    // 构建下载任务
    let task = core::DownloadTask::from_args(uid, &args)?;
    
    // 执行下载
    downloader.download(task).await?;
    
    Ok(())
}
