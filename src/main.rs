/*!
 * Pixiv 标签下载器 (Rust 版本)
 * 
 * 作者: Mannix Sun <<EMAIL>>
 * 
 * 这是一个用于根据标签下载 Pixiv 用户作品的 Rust 应用程序。
 * 支持图片、插画、漫画和小说的下载，提供高效的并发处理和灵活的配置选项。
 */

use anyhow::Result;
use clap::Parser;
use std::path::PathBuf;
use tracing::{info, error};

// 模块声明
mod config;
mod api;
mod downloader;
mod storage;
mod cli;
mod core;
mod utils;

use config::Config;
use cli::{Args, interactive_mode};
use core::PixivDownloader;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统
    init_logging()?;

    println!("Pixiv 标签下载器启动");

    // 解析命令行参数
    let args = Args::parse();

    // 加载配置
    println!("正在加载配置文件...");
    let config = match load_config(&args.config).await {
        Ok(config) => {
            println!("配置文件加载成功");
            config
        },
        Err(e) => {
            eprintln!("配置加载失败: {}", e);
            return Ok(()); // 改为正常退出，避免错误传播
        }
    };

    // 验证配置
    println!("正在验证配置...");
    if let Err(e) = config.validate() {
        eprintln!("配置验证失败: {}", e);
        return Ok(());
    }

    // 创建下载器实例
    println!("正在创建下载器实例...");
    let downloader = match PixivDownloader::new(config).await {
        Ok(downloader) => downloader,
        Err(e) => {
            eprintln!("下载器创建失败: {}", e);
            return Ok(());
        }
    };

    // 根据参数决定运行模式
    if args.is_interactive_mode() {
        // 交互式模式
        println!("进入交互式模式");
        interactive_mode(downloader).await?;
    } else {
        // 命令行模式
        println!("使用命令行参数模式");
        run_with_args(downloader, args).await?;
    }

    println!("程序执行完成");
    Ok(())
}

/// 初始化日志系统
fn init_logging() -> Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, fmt, EnvFilter};
    use std::fs::OpenOptions;

    // 创建日志目录
    std::fs::create_dir_all("logs").ok();

    // 创建日志文件
    let log_file = OpenOptions::new()
        .create(true)
        .append(true)
        .open("logs/pixiv_downloader.log")?;

    // 设置日志级别
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| "info".into());

    // 创建格式化层
    let fmt_layer = fmt::layer()
        .with_target(false)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true);

    // 创建文件输出层
    let file_layer = fmt::layer()
        .with_writer(log_file)
        .with_target(true)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true)
        .with_ansi(false);

    // 初始化订阅器
    tracing_subscriber::registry()
        .with(env_filter)
        .with(fmt_layer)
        .with(file_layer)
        .init();

    Ok(())
}

/// 加载配置文件
async fn load_config(config_path: &Option<PathBuf>) -> Result<Config> {
    let path = config_path
        .clone()
        .unwrap_or_else(|| PathBuf::from("config.yaml"));
    
    Config::load(&path).await
}

/// 使用命令行参数运行
async fn run_with_args(downloader: PixivDownloader, args: Args) -> Result<()> {
    // 验证必需参数
    let uid = args.uid.ok_or_else(|| {
        anyhow::anyhow!("命令行模式下必须指定用户 UID (--uid)")
    })?;
    
    // 构建下载任务
    let task = core::DownloadTask::from_args(uid, &args)?;
    
    // 执行下载
    let result = downloader.download(task).await?;

    // 输出下载结果
    println!("下载完成！");
    println!("成功下载: {} 个作品", result.successful_downloads);
    println!("失败下载: {} 个作品", result.failed_downloads);
    println!("跳过下载: {} 个作品", result.skipped_downloads);

    if !result.errors.is_empty() {
        println!("错误详情:");
        for error in &result.errors {
            println!("  作品 {}: {}", error.pid, error.error);
        }
    }

    Ok(())
}
