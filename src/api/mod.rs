/*!
 * Pixiv API 交互模块
 */

pub mod client;
pub mod auth;
pub mod endpoints;

use anyhow::Result;
use std::sync::Arc;

pub use client::*;
pub use auth::*;
pub use endpoints::*;

use crate::config::Config;
use crate::core::{ArtworkInfo, UserInfo};

/// Pixiv API 客户端
pub struct PixivClient {
    /// HTTP 客户端
    http_client: reqwest::Client,
    /// 配置
    config: Arc<Config>,
    /// 认证管理器
    auth: AuthManager,
}

impl PixivClient {
    /// 创建新的 API 客户端
    pub async fn new(config: Arc<Config>) -> Result<Self> {
        let http_client = create_http_client(&config)?;
        let auth = AuthManager::new(&config.auth)?;
        
        let client = Self {
            http_client,
            config,
            auth,
        };
        
        // 验证认证
        client.verify_authentication().await?;
        
        Ok(client)
    }
    
    /// 验证认证
    async fn verify_authentication(&self) -> Result<()> {
        // 检查是否为占位符认证信息
        if self.config.auth.session_id == "your_session_id_here" ||
           self.config.auth.csrf_token == "your_csrf_token_here" {
            return Err(anyhow::anyhow!("请在配置文件中填入有效的认证信息"));
        }

        // 暂时跳过实际的网络验证，避免在开发阶段出错
        // TODO: 在完整实现时启用网络验证
        /*
        let response = self.http_client
            .get("https://www.pixiv.net/ajax/user/self")
            .headers(self.auth.get_headers()?)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("认证验证失败，请检查配置文件中的认证信息"));
        }
        */

        Ok(())
    }
    
    /// 获取用户信息
    pub async fn get_user_info(&self, uid: u64) -> Result<UserInfo> {
        let url = format!("https://www.pixiv.net/ajax/user/{}", uid);
        
        let response = self.http_client
            .get(&url)
            .headers(self.auth.get_headers()?)
            .send()
            .await?;
        
        if !response.status().is_success() {
            return Err(anyhow::anyhow!("获取用户信息失败: HTTP {}", response.status()));
        }
        
        let json: serde_json::Value = response.json().await?;
        parse_user_info(uid, &json)
    }
    
    /// 获取用户作品列表
    pub async fn get_user_artworks(&self, uid: u64) -> Result<Vec<ArtworkInfo>> {
        let mut artworks = Vec::new();
        let mut offset = 0;
        const LIMIT: u32 = 48; // Pixiv 默认每页数量
        
        loop {
            let url = format!(
                "https://www.pixiv.net/ajax/user/{}/profile/all",
                uid
            );
            
            let response = self.http_client
                .get(&url)
                .headers(self.auth.get_headers()?)
                .send()
                .await?;
            
            if !response.status().is_success() {
                return Err(anyhow::anyhow!("获取作品列表失败: HTTP {}", response.status()));
            }
            
            let json: serde_json::Value = response.json().await?;
            let batch_artworks = parse_artworks_list(&json)?;
            
            if batch_artworks.is_empty() {
                break;
            }
            
            artworks.extend(batch_artworks);
            offset += LIMIT;
            
            // 添加延迟避免频率限制
            let delay = rand::random::<u64>() % 
                (self.config.download.delay_range[1] - self.config.download.delay_range[0] + 1) +
                self.config.download.delay_range[0];
            tokio::time::sleep(tokio::time::Duration::from_secs(delay)).await;
        }
        
        Ok(artworks)
    }
    
    /// 获取作品详细信息
    pub async fn get_artwork_detail(&self, pid: u64) -> Result<ArtworkInfo> {
        let url = format!("https://www.pixiv.net/ajax/illust/{}", pid);
        
        let response = self.http_client
            .get(&url)
            .headers(self.auth.get_headers()?)
            .send()
            .await?;
        
        if !response.status().is_success() {
            return Err(anyhow::anyhow!("获取作品详情失败: HTTP {}", response.status()));
        }
        
        let json: serde_json::Value = response.json().await?;
        parse_artwork_detail(&json)
    }
    
    /// 下载文件
    pub async fn download_file(&self, url: &str) -> Result<reqwest::Response> {
        let response = self.http_client
            .get(url)
            .headers(self.auth.get_headers()?)
            .send()
            .await?;
        
        if !response.status().is_success() {
            return Err(anyhow::anyhow!("文件下载失败: HTTP {}", response.status()));
        }
        
        Ok(response)
    }
}

/// 创建 HTTP 客户端
fn create_http_client(config: &Config) -> Result<reqwest::Client> {
    let mut builder = reqwest::Client::builder()
        .user_agent(&config.network.user_agent)
        .timeout(tokio::time::Duration::from_secs(config.download.timeout));
    
    // 设置代理（如果配置了）
    if let Some(proxy_url) = &config.network.proxy {
        let proxy = reqwest::Proxy::all(proxy_url)?;
        builder = builder.proxy(proxy);
    }
    
    Ok(builder.build()?)
}
