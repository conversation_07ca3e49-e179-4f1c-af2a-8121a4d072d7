/*!
 * 配置管理模块
 * 
 * 负责 YAML 配置文件的加载、验证和默认配置生成
 */

pub mod yaml_config;
pub mod default_config;

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;
use tracing::{info, warn};

pub use yaml_config::*;
pub use default_config::*;

/// 主配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// 认证配置
    pub auth: AuthConfig,
    /// 下载配置
    pub download: DownloadConfig,
    /// 存储配置
    pub storage: StorageConfig,
    /// 网络配置
    pub network: NetworkConfig,
    /// 日志配置
    pub logging: LoggingConfig,
}

/// 认证配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthConfig {
    /// Pixiv 会话 ID
    pub session_id: String,
    /// CSRF 令牌
    pub csrf_token: String,
}

/// 下载配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadConfig {
    /// 并发数量
    pub concurrency: usize,
    /// 延迟范围（秒）
    pub delay_range: [u64; 2],
    /// 重试次数
    pub retry_count: u32,
    /// 超时时间（秒）
    pub timeout: u64,
}

/// 存储配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    /// 输出根目录
    pub output_dir: String,
    /// 单图作品配置
    pub single_image: PathTemplateConfig,
    /// 多图作品配置
    pub multi_image: PathTemplateConfig,
    /// 小说配置
    pub novel: PathTemplateConfig,
    /// 文件冲突处理策略
    pub conflict_resolution: ConflictResolution,
}

/// 路径模板配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PathTemplateConfig {
    /// 路径模板
    pub path_template: String,
    /// 文件名模板
    pub filename_template: String,
}

/// 文件冲突处理策略
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ConflictResolution {
    /// 跳过已存在文件
    Skip,
    /// 覆盖已存在文件
    Overwrite,
    /// 重命名新文件
    Rename,
}

/// 网络配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    /// User-Agent
    pub user_agent: String,
    /// Referer
    pub referer: String,
    /// 代理设置（可选）
    pub proxy: Option<String>,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 日志文件路径（可选）
    pub file: Option<String>,
    /// 是否输出到控制台
    pub console: bool,
}

impl Config {
    /// 从文件加载配置
    pub async fn load(path: &Path) -> Result<Self> {
        if !path.exists() {
            warn!("配置文件不存在，正在创建默认配置: {}", path.display());
            create_default_config(path).await?;
            info!("已创建示例配置文件: {}", path.display());
            info!("请编辑配置文件中的认证信息后重新运行程序");
            return Err(anyhow::anyhow!("配置文件已创建，请编辑后重新运行"));
        }

        load_config_from_file(path).await
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        // 验证认证信息
        if self.auth.session_id.is_empty() {
            return Err(anyhow::anyhow!("session_id 不能为空"));
        }
        
        if self.auth.csrf_token.is_empty() {
            return Err(anyhow::anyhow!("csrf_token 不能为空"));
        }
        
        // 验证下载配置
        if self.download.concurrency == 0 {
            return Err(anyhow::anyhow!("并发数量必须大于 0"));
        }
        
        if self.download.delay_range[0] > self.download.delay_range[1] {
            return Err(anyhow::anyhow!("延迟范围配置错误"));
        }
        
        // 验证存储配置
        if self.storage.output_dir.is_empty() {
            return Err(anyhow::anyhow!("输出目录不能为空"));
        }
        
        Ok(())
    }
}

impl Default for Config {
    fn default() -> Self {
        Self {
            auth: AuthConfig {
                session_id: "your_session_id_here".to_string(),
                csrf_token: "your_csrf_token_here".to_string(),
            },
            download: DownloadConfig {
                concurrency: 3,
                delay_range: [1, 3],
                retry_count: 3,
                timeout: 30,
            },
            storage: StorageConfig {
                output_dir: "./Output".to_string(),
                single_image: PathTemplateConfig {
                    path_template: "{uid}_{username}/单图作品/{series_title_or_未分类}".to_string(),
                    filename_template: "{upload_date:%Y%m%d}_{pid}_{title}.{ext}".to_string(),
                },
                multi_image: PathTemplateConfig {
                    path_template: "{uid}_{username}/多图作品/{series_title_or_未分类}/{pid}_{title}".to_string(),
                    filename_template: "{upload_date:%Y%m%d}_{pid}_p{page_index}_{title}.{ext}".to_string(),
                },
                novel: PathTemplateConfig {
                    path_template: "{uid}_{username}/小说/{series_title_or_未分类}".to_string(),
                    filename_template: "{upload_date:%Y%m%d}_{pid}_{title}.txt".to_string(),
                },
                conflict_resolution: ConflictResolution::Skip,
            },
            network: NetworkConfig {
                user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36".to_string(),
                referer: "https://www.pixiv.net/".to_string(),
                proxy: None,
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file: Some("./pixiv_downloader.log".to_string()),
                console: true,
            },
        }
    }
}
