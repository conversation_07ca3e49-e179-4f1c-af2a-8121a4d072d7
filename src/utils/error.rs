/*!
 * 错误类型定义
 */

use thiserror::Error;

/// 应用程序错误类型
#[derive(Error, Debug)]
pub enum AppError {
    /// 配置错误
    #[error("配置错误: {0}")]
    Config(String),

    /// 网络错误
    #[error("网络连接失败: {0}")]
    Network(#[from] reqwest::Error),

    /// 文件系统错误
    #[error("文件操作失败: {0}")]
    FileSystem(#[from] std::io::Error),

    /// JSON 解析错误
    #[error("数据解析失败: {0}")]
    Json(#[from] serde_json::Error),

    /// YAML 解析错误
    #[error("配置文件格式错误: {0}")]
    Yaml(#[from] serde_yaml::Error),

    /// 认证错误
    #[error("身份验证失败: {0}")]
    Authentication(String),

    /// API 错误
    #[error("Pixiv API 调用失败: {0}")]
    Api(String),

    /// 下载错误
    #[error("文件下载失败: {0}")]
    Download(String),

    /// 用户输入错误
    #[error("输入参数错误: {0}")]
    Input(String),

    /// 权限错误
    #[error("权限不足: {0}")]
    Permission(String),

    /// 其他错误
    #[error("未知错误: {0}")]
    Other(String),
}

impl AppError {
    /// 创建配置错误
    pub fn config<T: Into<String>>(msg: T) -> Self {
        Self::Config(msg.into())
    }

    /// 创建认证错误
    pub fn auth<T: Into<String>>(msg: T) -> Self {
        Self::Authentication(msg.into())
    }

    /// 创建API错误
    pub fn api<T: Into<String>>(msg: T) -> Self {
        Self::Api(msg.into())
    }

    /// 创建下载错误
    pub fn download<T: Into<String>>(msg: T) -> Self {
        Self::Download(msg.into())
    }

    /// 创建输入错误
    pub fn input<T: Into<String>>(msg: T) -> Self {
        Self::Input(msg.into())
    }

    /// 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        match self {
            Self::Config(msg) => format!("配置文件有问题: {}", msg),
            Self::Network(err) => {
                if err.is_timeout() {
                    "网络连接超时，请检查网络连接".to_string()
                } else if err.is_connect() {
                    "无法连接到 Pixiv 服务器，请检查网络连接".to_string()
                } else {
                    format!("网络错误: {}", err)
                }
            }
            Self::FileSystem(err) => {
                match err.kind() {
                    std::io::ErrorKind::PermissionDenied => "文件权限不足，请检查文件夹权限".to_string(),
                    std::io::ErrorKind::NotFound => "文件或目录不存在".to_string(),
                    std::io::ErrorKind::AlreadyExists => "文件已存在".to_string(),
                    _ => format!("文件操作失败: {}", err),
                }
            }
            Self::Authentication(msg) => format!("登录验证失败: {}", msg),
            Self::Api(msg) => format!("Pixiv 服务器返回错误: {}", msg),
            Self::Download(msg) => format!("下载失败: {}", msg),
            Self::Input(msg) => format!("输入有误: {}", msg),
            Self::Permission(msg) => format!("权限不足: {}", msg),
            _ => self.to_string(),
        }
    }

    /// 获取错误建议
    pub fn suggestion(&self) -> Option<String> {
        match self {
            Self::Config(_) => Some("请检查 config.yaml 文件的格式和内容".to_string()),
            Self::Network(_) => Some("请检查网络连接，或稍后重试".to_string()),
            Self::Authentication(_) => Some("请检查配置文件中的 session_id 和 csrf_token 是否正确".to_string()),
            Self::Api(_) => Some("请稍后重试，或检查用户 ID 是否正确".to_string()),
            Self::FileSystem(_) => Some("请检查文件夹权限和磁盘空间".to_string()),
            Self::Permission(_) => Some("请以管理员权限运行程序".to_string()),
            _ => None,
        }
    }
}

/// 结果类型别名
pub type AppResult<T> = Result<T, AppError>;
