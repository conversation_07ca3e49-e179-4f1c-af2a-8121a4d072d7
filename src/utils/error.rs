/*!
 * 错误类型定义
 */

use thiserror::Error;

/// 应用程序错误类型
#[derive(Error, Debug)]
pub enum AppError {
    /// 配置错误
    #[error("配置错误: {0}")]
    Config(String),
    
    /// 网络错误
    #[error("网络错误: {0}")]
    Network(#[from] reqwest::Error),
    
    /// 文件系统错误
    #[error("文件系统错误: {0}")]
    FileSystem(#[from] std::io::Error),
    
    /// JSON 解析错误
    #[error("JSON 解析错误: {0}")]
    Json(#[from] serde_json::Error),
    
    /// YAML 解析错误
    #[error("YAML 解析错误: {0}")]
    Yaml(#[from] serde_yaml::Error),
    
    /// 认证错误
    #[error("认证错误: {0}")]
    Authentication(String),
    
    /// API 错误
    #[error("API 错误: {0}")]
    Api(String),
    
    /// 其他错误
    #[error("其他错误: {0}")]
    Other(String),
}
