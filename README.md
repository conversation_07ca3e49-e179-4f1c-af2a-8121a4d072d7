# Pixiv 标签下载器 (Rust 版本)

一个用于根据标签下载 Pixiv 用户作品的 Rust 应用程序，支持图片、插画、漫画和小说的下载，提供高效的并发处理和灵活的配置选项。

## 功能特性

- ✅ **多种作品类型支持**: 插画、漫画、小说
- ✅ **灵活的标签过滤**: 支持 AND/OR/NOT 逻辑
- ✅ **并发下载**: 高效的异步并发处理
- ✅ **路径模板**: 灵活的文件组织和命名
- ✅ **交互式界面**: 用户友好的命令行交互
- ✅ **中文本地化**: 完整的中文界面和错误消息
- ✅ **YAML 配置**: 易于编辑的配置文件
- ✅ **元数据保存**: 自动生成作品元数据文件

## 安装和使用

### 前置要求

- Rust 1.70+ 
- 有效的 Pixiv 账号

### 编译安装

```bash
git clone https://github.com/mannixsun/PixivTagDownloader_Rust.git
cd PixivTagDownloader_Rust
cargo build --release
```

### 配置设置

首次运行程序会自动创建配置文件 `config.yaml`：

```bash
cargo run
```

编辑 `config.yaml` 文件，填入你的 Pixiv 认证信息：

```yaml
auth:
  session_id: "your_session_id_here"  # 从浏览器 Cookie 中获取
  csrf_token: "your_csrf_token_here"  # 从浏览器 Cookie 中获取
```

### 获取认证信息

1. 在浏览器中登录 Pixiv
2. 打开开发者工具 (F12)
3. 转到 Network 标签页
4. 刷新页面
5. 在请求头中找到 `PHPSESSID` (session_id) 和 `tt` (csrf_token)

### 使用方法

#### 交互式模式（推荐）

```bash
cargo run
```

程序会引导你完成以下步骤：
1. 输入 Pixiv 用户 UID
2. 选择标签过滤条件
3. 选择作品类型
4. 确认并开始下载

#### 命令行模式

```bash
# 下载用户所有作品
cargo run -- --uid 123456 --all

# 下载包含特定标签的作品
cargo run -- --uid 123456 --tags "原创,插画" --logic or

# 只下载插画类型
cargo run -- --uid 123456 --type illust

# 自定义输出目录
cargo run -- --uid 123456 --output-dir "./Downloads"
```

#### 命令行参数

- `--uid <UID>`: Pixiv 用户 ID（必需）
- `--tags <TAGS>`: 标签列表，用逗号分隔
- `--logic <LOGIC>`: 标签过滤逻辑 (and/or/not)
- `--type <TYPE>`: 作品类型 (illust/manga/novel/all)
- `--all`: 下载所有作品，忽略标签过滤
- `--output-dir <DIR>`: 自定义输出目录
- `--config <FILE>`: 自定义配置文件路径
- `--concurrency <NUM>`: 并发下载数量
- `--help`: 显示帮助信息

## 配置文件说明

### 路径模板变量

配置文件支持以下路径模板变量：

- `{uid}` - Pixiv 用户 ID
- `{username}` - Pixiv 用户名
- `{pid}` - 作品 ID
- `{title}` - 作品标题
- `{type}` - 作品类型
- `{page_index}` - 图片页码索引号
- `{page_count}` - 作品总页数
- `{series_title}` - 系列名称
- `{upload_date}` - 上传日期
- `{tags}` - 作品标签
- `{r18}` - 是否为 R18 作品
- `{ext}` - 文件扩展名

### 示例配置

```yaml
storage:
  output_dir: "./Output"
  single_image:
    path_template: "{uid}_{username}/单图作品/{series_title_or_未分类}"
    filename_template: "{upload_date:%Y%m%d}_{pid}_{title}.{ext}"
  multi_image:
    path_template: "{uid}_{username}/多图作品/{series_title_or_未分类}/{pid}_{title}"
    filename_template: "{upload_date:%Y%m%d}_{pid}_p{page_index}_{title}.{ext}"
```

## 开发状态

当前版本实现了核心功能的基础架构：

- ✅ 项目结构和配置系统
- ✅ API 客户端框架
- ✅ 并发下载引擎
- ✅ 交互式界面
- ✅ 路径模板系统
- 🚧 完整的 API 实现（需要真实认证测试）
- 🚧 小说下载功能
- 🚧 进度显示和错误恢复
- 🚧 单元测试和集成测试

## 注意事项

1. **遵守 Pixiv 服务条款**: 请合理使用，避免过于频繁的请求
2. **尊重版权**: 下载的作品仅供个人学习和欣赏
3. **网络稳定性**: 建议在稳定的网络环境下使用
4. **认证安全**: 请妥善保管你的认证信息

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 作者

Mannix Sun <<EMAIL>>
